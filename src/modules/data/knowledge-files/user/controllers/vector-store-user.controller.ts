import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { VectorStoreUserService } from '../services';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import {
  AssignFilesDto,
  AssignFilesResponseDto,
  CreateVectorStoreDto,
  QueryVectorStoreDto,
  UpdateVectorStoreDto,
  VectorStoreResponseDto,
} from '../dto';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import { ApiExtraModels } from '@nestjs/swagger';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { CurrentUser } from '@modules/auth/decorators';

@ApiTags(SWAGGER_API_TAGS.USER_KNOWLEDGE_FILES)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@ApiExtraModels(VectorStoreResponseDto, AssignFilesResponseDto, UpdateVectorStoreDto, PaginatedResult)
@Controller('user/vector-stores')
export class VectorStoreUserController {
  constructor(
    private readonly vectorStoreUserService: VectorStoreUserService,
  ) {}

  /**
   * Tạo vector store mới
   */
  @ApiOperation({ summary: 'Tạo vector store mới' })
  @ApiBody({ type: CreateVectorStoreDto })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo vector store thành công.',
    schema: ApiResponseDto.getSchema(VectorStoreResponseDto)
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Thông tin đầu vào không hợp lệ.',
    schema: {
      properties: {
        code: { type: 'number', example: 400 },
        message: { type: 'string', example: 'Thông tin đầu vào không hợp lệ.' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Lỗi máy chủ, vui lòng thử lại sau.',
    schema: {
      properties: {
        code: { type: 'number', example: 500 },
        message: {
          type: 'string',
          example: 'Lỗi máy chủ, vui lòng thử lại sau.',
        },
      },
    },
  })
  @Post()
  @HttpCode(HttpStatus.CREATED)
  async createVectorStore(
    @Body() createVectorStoreDto: CreateVectorStoreDto,
    @CurrentUser('id') userId: number,
  ) {
    const result = await this.vectorStoreUserService.createVectorStore(
      createVectorStoreDto,
      userId,
    );
    return ApiResponseDto.created(
      result,
      'Tạo vector store thành công.'
    );
  }

  /**
   * Lấy danh sách vector stores
   */
  @ApiOperation({ summary: 'Lấy danh sách vector stores' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách vector store thành công.',
    schema: ApiResponseDto.getPaginatedSchema(VectorStoreResponseDto)
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Thông tin đầu vào không hợp lệ.',
    schema: {
      properties: {
        code: { type: 'number', example: 400 },
        message: { type: 'string', example: 'Thông tin đầu vào không hợp lệ.' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Lỗi máy chủ, vui lòng thử lại sau.',
    schema: {
      properties: {
        code: { type: 'number', example: 500 },
        message: {
          type: 'string',
          example: 'Lỗi máy chủ, vui lòng thử lại sau.',
        },
      },
    },
  })
  @Get()
  @HttpCode(HttpStatus.OK)
  async getVectorStores(
    @Query() queryDto: QueryVectorStoreDto,
    @CurrentUser('id') userId: number,
  ) {
    const result = await this.vectorStoreUserService.getVectorStores(
      queryDto,
      userId,
    );
    return ApiResponseDto.paginated(
      result,
      'Lấy danh sách vector store thành công.'
    );
  }

  /**
   * Cập nhật thông tin vector store
   */
  @ApiOperation({ summary: 'Cập nhật thông tin vector store' })
  @ApiParam({ name: 'id', description: 'ID của vector store' })
  @ApiBody({ type: UpdateVectorStoreDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cập nhật vector store thành công.',
    schema: ApiResponseDto.getSchema(VectorStoreResponseDto)
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Vector store không tồn tại.',
    schema: {
      properties: {
        code: { type: 'number', example: 404 },
        message: { type: 'string', example: 'Vector store không tồn tại.' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Thông tin đầu vào không hợp lệ.',
    schema: {
      properties: {
        code: { type: 'number', example: 400 },
        message: { type: 'string', example: 'Thông tin đầu vào không hợp lệ.' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Lỗi máy chủ, vui lòng thử lại sau.',
    schema: {
      properties: {
        code: { type: 'number', example: 500 },
        message: {
          type: 'string',
          example: 'Lỗi máy chủ, vui lòng thử lại sau.',
        },
      },
    },
  })
  @Put(':id')
  @HttpCode(HttpStatus.OK)
  async updateVectorStore(
    @Param('id') id: string,
    @Body() updateVectorStoreDto: UpdateVectorStoreDto,
    @CurrentUser('id') userId: number,
  ) {
    const result = await this.vectorStoreUserService.updateVectorStore(
      id,
      updateVectorStoreDto,
      userId,
    );
    return ApiResponseDto.success(
      result,
      'Cập nhật vector store thành công.'
    );
  }

  /**
   * Lấy thông tin chi tiết vector store
   */
  @ApiOperation({ summary: 'Lấy thông tin chi tiết vector store' })
  @ApiParam({ name: 'id', description: 'ID của vector store' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy thông tin chi tiết vector store thành công.',
    schema: ApiResponseDto.getSchema(VectorStoreResponseDto)
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Vector store không tồn tại.',
    schema: {
      properties: {
        code: { type: 'number', example: 404 },
        message: { type: 'string', example: 'Vector store không tồn tại.' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Lỗi máy chủ, vui lòng thử lại sau.',
    schema: {
      properties: {
        code: { type: 'number', example: 500 },
        message: {
          type: 'string',
          example: 'Lỗi máy chủ, vui lòng thử lại sau.',
        },
      },
    },
  })
  @Get(':id')
  @HttpCode(HttpStatus.OK)
  async getVectorStoreDetail(
    @Param('id') id: string,
    @CurrentUser('id') userId: number,
  ) {
    const result = await this.vectorStoreUserService.getVectorStoreDetail(
      id,
      userId,
    );
    return ApiResponseDto.success(
      result,
      'Lấy thông tin chi tiết vector store thành công.'
    );
  }

  /**
   * Gán file vào vector store và lấy file ID từ RAG API với tùy chọn chunk_size và chunk_overlap
   */
  @ApiOperation({
    summary: 'Gán file vào vector store và lấy file ID từ RAG API',
    description: 'Gán các file vào vector store với khả năng tùy chỉnh kích thước chunk (chunk_size) và độ chồng lấp giữa các chunk (chunk_overlap). Mặc định: chunk_size=2000, chunk_overlap=100.'
  })
  @ApiParam({ name: 'id', description: 'ID của vector store' })
  @ApiBody({
    type: AssignFilesDto,
    description: 'Danh sách ID của các file cần gán và thông số xử lý chunk',
    examples: {
      'Basic': {
        summary: 'Gán file với cài đặt mặc định',
        description: 'Sử dụng chunk_size=2000 và chunk_overlap=100 mặc định',
        value: {
          fileIds: [
            'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
            'b2c3d4e5-f6a7-8901-bcde-f01234567890'
          ]
        }
      },
      'WithChunkSettings': {
        summary: 'Gán file với cài đặt chunk tùy chỉnh',
        description: 'Tùy chỉnh kích thước chunk và độ chồng lấp',
        value: {
          fileIds: [
            'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
            'b2c3d4e5-f6a7-8901-bcde-f01234567890'
          ],
          chunkSize: 2000,
          chunkOverlap: 100
        }
      },
      'LargeChunks': {
        summary: 'Gán file với chunk lớn',
        description: 'Sử dụng chunk lớn hơn cho tài liệu dài',
        value: {
          fileIds: [
            'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
          ],
          chunkSize: 4000,
          chunkOverlap: 200
        }
      },
      'SmallChunks': {
        summary: 'Gán file với chunk nhỏ',
        description: 'Sử dụng chunk nhỏ hơn cho tài liệu ngắn hoặc cần độ chính xác cao',
        value: {
          fileIds: [
            'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
          ],
          chunkSize: 1000,
          chunkOverlap: 50
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Đã nhận file ID từ RAG API và gán vào vector store thành công.',
    schema: ApiResponseDto.getSchema(AssignFilesResponseDto)
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Lỗi khi gán file vào vector store.',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Vector store hoặc file không tồn tại.',
    schema: {
      properties: {
        code: { type: 'number', example: 404 },
        message: {
          type: 'string',
          example: 'Vector store với ID vs_123456 không tồn tại hoặc bạn không có quyền truy cập',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Lỗi máy chủ, vui lòng thử lại sau.',
    schema: {
      properties: {
        code: { type: 'number', example: 500 },
        message: {
          type: 'string',
          example: 'Lỗi máy chủ, vui lòng thử lại sau.',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Lỗi khi gán file vào vector store.',
    schema: {
      properties: {
        code: { type: 'number', example: 20206 },
        message: {
          type: 'string',
          example: 'Lỗi khi gán file vào vector store: Không có file nào tồn tại hoặc bạn không có quyền truy cập',
        },
      },
    },
  })
  @Post(':id/files')
  @HttpCode(HttpStatus.OK)
  async assignFilesToVectorStore(
    @Param('id') id: string,
    @Body() assignFilesDto: AssignFilesDto,
    @CurrentUser('id') userId: number,
  ) {
    const result = await this.vectorStoreUserService.assignFilesToVectorStore(
      id,
      assignFilesDto,
      userId,
    );
    return ApiResponseDto.success(
      result,
      'Đã nhận file ID từ RAG API và gán vào vector store thành công.'
    );
  }

  /**
   * Xóa file khỏi vector store
   */
  @ApiOperation({ summary: 'Xóa file khỏi vector store' })
  @ApiParam({ name: 'id', description: 'ID của vector store' })
  @ApiParam({ name: 'fileId', description: 'ID của file cần xóa' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa file khỏi vector store thành công.',
    schema: ApiResponseDto.getSchema(Object)
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Vector store hoặc file không tồn tại.',
    schema: {
      properties: {
        code: { type: 'number', example: 404 },
        message: {
          type: 'string',
          example: 'Vector store hoặc file không tồn tại.',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Lỗi máy chủ, vui lòng thử lại sau.',
    schema: {
      properties: {
        code: { type: 'number', example: 500 },
        message: {
          type: 'string',
          example: 'Lỗi máy chủ, vui lòng thử lại sau.',
        },
      },
    },
  })
  @Delete(':id/files/:fileId')
  @HttpCode(HttpStatus.OK)
  async removeFileFromVectorStore(
    @Param('id') id: string,
    @Param('fileId') fileId: string,
    @CurrentUser ('id') userId: number,
  ) {
    const result = await this.vectorStoreUserService.removeFilesFromVectorStore(
      id,
      [fileId],
      userId,
    );
    return ApiResponseDto.success(
      result,
      'Xóa file khỏi vector store thành công.'
    );
  }

  /**
   * Xóa nhiều file khỏi vector store
   */
  @ApiOperation({ summary: 'Xóa nhiều file khỏi vector store' })
  @ApiParam({ name: 'id', description: 'ID của vector store' })
  @ApiBody({ type: AssignFilesDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa file khỏi vector store thành công.',
    schema: ApiResponseDto.getSchema(Object)
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Vector store không tồn tại.',
    schema: {
      properties: {
        code: { type: 'number', example: 404 },
        message: { type: 'string', example: 'Vector store không tồn tại.' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Lỗi máy chủ, vui lòng thử lại sau.',
    schema: {
      properties: {
        code: { type: 'number', example: 500 },
        message: {
          type: 'string',
          example: 'Lỗi máy chủ, vui lòng thử lại sau.',
        },
      },
    },
  })
  @Delete(':id/files')
  @HttpCode(HttpStatus.OK)
  async removeFilesFromVectorStore(
    @Param('id') id: string,
    @Body() assignFilesDto: AssignFilesDto,
    @CurrentUser('id') userId: number,
  ) {
    const result = await this.vectorStoreUserService.removeFilesFromVectorStore(
      id,
      assignFilesDto.fileIds,
      userId,
    );
    return ApiResponseDto.success(
      result,
      'Xóa file khỏi vector store thành công.'
    );
  }
}

// Simple test script to validate AssignFilesDto
const { validate } = require('class-validator');
const { plainToInstance } = require('class-transformer');

// Mock the DTO class for testing
class AssignFilesDto {
  constructor() {
    this.chunkSize = 2000;
    this.chunkOverlap = 100;
  }
}

// Test basic functionality
async function testBasicValidation() {
  console.log('Testing basic DTO validation...');
  
  // Test 1: Basic valid data
  const dto1 = {
    fileIds: ['a1b2c3d4-e5f6-7890-abcd-ef1234567890'],
    chunkSize: 2000,
    chunkOverlap: 100
  };
  console.log('✓ Test 1 - Basic valid data:', dto1);
  
  // Test 2: Custom chunk settings
  const dto2 = {
    fileIds: ['a1b2c3d4-e5f6-7890-abcd-ef1234567890'],
    chunkSize: 4000,
    chunkOverlap: 200
  };
  console.log('✓ Test 2 - Custom chunk settings:', dto2);
  
  // Test 3: Default values
  const dto3 = {
    fileIds: ['a1b2c3d4-e5f6-7890-abcd-ef1234567890']
  };
  console.log('✓ Test 3 - Default values (should use 2000/100):', dto3);
  
  // Test 4: Edge cases
  const dto4 = {
    fileIds: ['a1b2c3d4-e5f6-7890-abcd-ef1234567890'],
    chunkSize: 100,  // minimum
    chunkOverlap: 0  // minimum
  };
  console.log('✓ Test 4 - Edge cases (min values):', dto4);
  
  const dto5 = {
    fileIds: ['a1b2c3d4-e5f6-7890-abcd-ef1234567890'],
    chunkSize: 8000,  // maximum
    chunkOverlap: 1000  // maximum
  };
  console.log('✓ Test 5 - Edge cases (max values):', dto5);
  
  console.log('\n✅ All basic tests passed! The DTO structure supports chunk_size and chunk_overlap parameters.');
}

testBasicValidation().catch(console.error);
